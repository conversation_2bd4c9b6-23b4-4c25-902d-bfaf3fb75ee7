import { Component, Input, ChangeDetectionStrategy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Observable } from 'rxjs';
import { CodeViewerComponent } from '../../../code-viewer/code-viewer.component';
import { LoadingAnimationComponent } from '../../loading-animation/loading-animation.component';
import { FileModel } from '../../../code-viewer/code-viewer.component';

@Component({
  selector: 'app-editor-view',
  standalone: true,
  imports: [CommonModule, CodeViewerComponent, LoadingAnimationComponent],
  template: `
    <div class="editor-view">
      <!-- Always show loading animation when code generation is not complete -->
      <app-loading-animation
        *ngIf="!isCodeGenerationComplete"
        [messages]="loadingMessages"
        [theme]="(currentTheme | async) || 'light'">
      </app-loading-animation>

      <!-- Show code viewer when code generation is complete -->
      <app-code-viewer
        *ngIf="isCodeGenerationComplete"
        [theme]="(currentTheme | async) || 'light'"
        [files]="(files | async) || []"
        [showFileExplorer]="true">
      </app-code-viewer>
    </div>
  `,
  styleUrls: ['./editor-view.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class EditorViewComponent {
  @Input() currentTheme!: Observable<'light' | 'dark'>;
  @Input() files!: Observable<FileModel[]>;
  @Input() isCodeGenerationComplete: boolean = false;
  @Input() loadingMessages: string[] = [];
}
