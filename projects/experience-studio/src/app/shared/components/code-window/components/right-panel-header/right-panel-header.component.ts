import { Component, Input, Output, EventEmitter, ChangeDetectionStrategy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IconsComponent } from '@awe/play-comp-library';
import { Observable } from 'rxjs';

export interface TabClickEvent {
  tab: 'overview' | 'preview' | 'editor' | 'logs' | 'artifacts';
}

export interface IconClickEvent {
  action: 'edit' | 'external-link' | 'export';
}

@Component({
  selector: 'app-right-panel-header',
  standalone: true,
  imports: [CommonModule, IconsComponent],
  template: `
    <div class="custom-header" [ngClass]="(currentTheme | async) + '-theme'">
      <div class="header-left">
        <!-- Home icon that only shows when left panel is collapsed -->
        <awe-icons
          *ngIf="isLeftPanelCollapsed | async"
          iconName="awe_home"
          iconColor="neutralIcon"
          class="cursor-pointer"
          (click)="homeClicked.emit()"
          title="Navigate to home page">
        </awe-icons>
        <awe-icons
          class="dockToRight"
          *ngIf="isLeftPanelCollapsed | async"
          (click)="toggleLeftPanelClicked.emit()"
          iconName="awe_dock_to_right"
          iconColor="neutralIcon"
          title="Toggle left panel">
        </awe-icons>

        <!-- Tabs container -->
        <div class="tabs-container">
          <!-- UI Design Mode - Overview and Preview Tabs -->
          <ng-container *ngIf="isUIDesignMode | async">
            <!-- Overview tab -->
            <div
              *ngIf="showUIDesignOverviewTab | async"
              class="custom-button"
              [class.active]="(currentView | async) === 'overview'"
              (click)="onTabClick('overview')"
              [title]="'View UI Design in Mobile Frame'">
              <span>Overview</span>
            </div>
            <!-- Preview tab -->
            <div
              class="custom-button"
              [class.active]="(currentView | async) === 'preview' || !(showUIDesignOverviewTab | async)"
              (click)="onTabClick('preview')"
              [title]="'View UI Design Canvas'">
              <span>Preview</span>
            </div>
          </ng-container>

          <!-- Standard Mode - All Tabs -->
          <ng-container *ngIf="!(isUIDesignMode | async)">
            <!-- Preview tab -->
            <div
              *ngIf="isNewPreviewTabEnabled"
              class="custom-button"
              [class.active]="(currentView | async) === 'preview' && !(isLogsActive | async)"
              [class.error-tab]="isInFailedState"
              (click)="onTabClick('preview')"
              [title]="isInFailedState ? 'View error details' : 'View deployed application preview'">
              <span>{{ (previewTabName | async) || 'Preview' }}</span>
            </div>
            <!-- Code tab -->
            <div
              *ngIf="isNewCodeTabEnabled"
              class="custom-button"
              [class.active]="(currentView | async) === 'editor' && !(isLogsActive | async)"
              (click)="onTabClick('code')"
              [title]="'View generated code'">
              <span>Code</span>
            </div>
            <!-- Logs tab -->
            <div
              class="custom-button"
              [class.active]="isLogsActive | async"
              [class.disabled]="!hasLogs"
              (click)="hasLogs && onTabClick('logs')"
              [title]="hasLogs ? 'View application logs and debugging information' : 'Logs will be available once generated'">
              <span>Logs</span>
              <i *ngIf="isStreamingLogs" class="bi bi-arrow-repeat tab-status spinning"></i>
            </div>
            <!-- Artifacts tab -->
            <div
              class="custom-button"
              [class.active]="isArtifactsActive | async"
              [class.disabled]="!isArtifactsTabEnabled"
              (click)="isArtifactsTabEnabled && onTabClick('artifacts')"
              [title]="isArtifactsTabEnabled ? 'View project artifacts' : 'Artifacts will be available when generated'">
              <span>Artifacts</span>
            </div>
          </ng-container>
        </div>
      </div>

      <div class="header-right">
        <div class="icon-group">
          <!-- Export button -->
          <div
            *ngIf="isCodeGenerationComplete && !(previewError | async) && (currentView | async) === 'editor'"
            class="custom-button"
            [class.active]="isExperienceStudioModalOpen | async"
            (click)="onIconClick('export')"
            title="Export project">
            <span>Export</span>
          </div>
          <!-- Edit icon -->
          <awe-icons
            *ngIf="(currentView | async) === 'preview' && isCodeGenerationComplete && !(previewError | async)"
            iconName="awe_edit"
            iconColor="neutralIcon"
            title="Select element to edit"
            [class.active]="isElementSelectionMode"
            (click)="onIconClick('edit')"
            [disabled]="true">
          </awe-icons>
          <!-- External link icon -->
          <awe-icons
            *ngIf="(currentView | async) === 'preview' && isCodeGenerationComplete && !(previewError | async) && (deployedUrl | async)"
            iconName="awe_external_link"
            iconColor="neutralIcon"
            title="Open preview in new tab"
            (click)="onIconClick('external-link')">
          </awe-icons>
        </div>
      </div>
    </div>
  `,
  styleUrls: ['./right-panel-header.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class RightPanelHeaderComponent {
  // Input properties
  @Input() currentTheme!: Observable<'light' | 'dark'>;
  @Input() isLeftPanelCollapsed!: Observable<boolean>;
  @Input() isUIDesignMode!: Observable<boolean>;
  @Input() showUIDesignOverviewTab!: Observable<boolean>;
  @Input() currentView!: Observable<string>;
  @Input() isLogsActive!: Observable<boolean>;
  @Input() isArtifactsActive!: Observable<boolean>;
  @Input() previewTabName!: Observable<string>;
  @Input() isExperienceStudioModalOpen!: Observable<boolean>;
  @Input() previewError!: Observable<boolean>;
  @Input() deployedUrl!: Observable<string | null>;

  @Input() isNewPreviewTabEnabled: boolean = false;
  @Input() isNewCodeTabEnabled: boolean = false;
  @Input() isInFailedState: boolean = false;
  @Input() hasLogs: boolean = false;
  @Input() isStreamingLogs: boolean = false;
  @Input() isArtifactsTabEnabled: boolean = false;
  @Input() isCodeGenerationComplete: boolean = false;
  @Input() isElementSelectionMode: boolean = false;

  // Output events
  @Output() homeClicked = new EventEmitter<void>();
  @Output() toggleLeftPanelClicked = new EventEmitter<void>();
  @Output() tabClicked = new EventEmitter<TabClickEvent>();
  @Output() iconClicked = new EventEmitter<IconClickEvent>();

  onTabClick(tab: 'overview' | 'preview' | 'code' | 'logs' | 'artifacts'): void {
    const mappedTab = tab === 'code' ? 'editor' : tab;
    this.tabClicked.emit({ tab: mappedTab as any });
  }

  onIconClick(action: 'edit' | 'external-link' | 'export'): void {
    this.iconClicked.emit({ action });
  }
}
