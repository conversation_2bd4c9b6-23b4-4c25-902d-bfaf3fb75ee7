import { Component, Input, Output, EventEmitter, ChangeDetectionStrategy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IconsComponent } from '@awe/play-comp-library';
import { Observable } from 'rxjs';

@Component({
  selector: 'app-left-panel-header',
  standalone: true,
  imports: [CommonModule, IconsComponent],
  template: `
    <div class="custom-header" [ngClass]="(currentTheme | async) + '-theme'">
      <div class="header-left">
        <awe-icons
          iconName="awe_home"
          iconColor="neutralIcon"
          class="cursor-pointer"
          (click)="homeClicked.emit()"
          title="Navigate to home page">
        </awe-icons>
        <awe-icons
          (click)="toggleLeftPanelClicked.emit()"
          iconName="awe_dock_to_right"
          iconColor="neutralIcon">
        </awe-icons>
      </div>
      <div class="header-center">
        <div
          class="project-name"
          [class.shimmer]="isProjectNameLoading"
          [class.hidden]="shouldHideProjectName | async"
          *ngIf="!(shouldHideProjectName | async)">
          {{ projectName }}
        </div>
      </div>
      <div class="header-right">
        <!-- Future: History tab can be added here -->
      </div>
    </div>
  `,
  styleUrls: ['./left-panel-header.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class LeftPanelHeaderComponent {
  @Input() currentTheme!: Observable<'light' | 'dark'>;
  @Input() shouldHideProjectName!: Observable<boolean>;
  @Input() projectName: string | null = null;
  @Input() isProjectNameLoading: boolean = false;

  @Output() homeClicked = new EventEmitter<void>();
  @Output() toggleLeftPanelClicked = new EventEmitter<void>();
}
