// Right Panel Header Styles
.custom-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  border-bottom: 1px solid var(--border-color);
  background: var(--header-background);
  min-height: 48px;

  .header-left {
    display: flex;
    align-items: center;
    gap: 12px;

    .dockToRight {
      margin-right: 8px;
    }

    .tabs-container {
      display: flex;
      align-items: center;
      gap: 4px;

      .custom-button {
        padding: 8px 16px;
        border-radius: 6px;
        background: transparent;
        border: 1px solid transparent;
        color: var(--text-secondary);
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
        display: flex;
        align-items: center;
        gap: 6px;

        &:hover:not(.disabled) {
          background: var(--button-hover);
          color: var(--text-primary);
        }

        &.active {
          background: var(--button-active);
          color: var(--text-active);
          border-color: var(--border-active);
        }

        &.disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }

        &.error-tab {
          color: var(--error-color);
          border-color: var(--error-border);
          
          &.active {
            background: var(--error-background);
          }
        }

        .tab-status {
          font-size: 12px;
          
          &.spinning {
            animation: spin 1s linear infinite;
          }
        }
      }
    }
  }

  .header-right {
    display: flex;
    align-items: center;

    .icon-group {
      display: flex;
      align-items: center;
      gap: 8px;

      .custom-button {
        padding: 8px 16px;
        border-radius: 6px;
        background: transparent;
        border: 1px solid transparent;
        color: var(--text-secondary);
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;

        &:hover {
          background: var(--button-hover);
          color: var(--text-primary);
        }

        &.active {
          background: var(--button-active);
          color: var(--text-active);
          border-color: var(--border-active);
        }
      }

      awe-icons {
        cursor: pointer;
        transition: opacity 0.2s ease;

        &:hover {
          opacity: 0.8;
        }

        &.active {
          opacity: 1;
        }

        &[disabled] {
          opacity: 0.5;
          cursor: not-allowed;
        }
      }
    }
  }

  &.light-theme {
    --header-background: #ffffff;
    --border-color: #e5e7eb;
    --text-primary: #1f2937;
    --text-secondary: #6b7280;
    --text-active: #3b82f6;
    --button-hover: #f3f4f6;
    --button-active: #eff6ff;
    --border-active: #3b82f6;
    --error-color: #ef4444;
    --error-border: #fecaca;
    --error-background: #fef2f2;
  }

  &.dark-theme {
    --header-background: #1f2937;
    --border-color: #374151;
    --text-primary: #f9fafb;
    --text-secondary: #9ca3af;
    --text-active: #60a5fa;
    --button-hover: #374151;
    --button-active: #1e3a8a;
    --border-active: #60a5fa;
    --error-color: #f87171;
    --error-border: #7f1d1d;
    --error-background: #7f1d1d;
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.cursor-pointer {
  cursor: pointer;
}
