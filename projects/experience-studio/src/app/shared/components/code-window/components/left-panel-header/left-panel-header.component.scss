// Left Panel Header Styles
.custom-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  border-bottom: 1px solid var(--border-color);
  background: var(--header-background);
  min-height: 48px;

  .header-left {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .header-center {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;

    .project-name {
      font-weight: 600;
      font-size: 14px;
      color: var(--text-primary);
      text-align: center;
      max-width: 200px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;

      &.shimmer {
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200% 100%;
        animation: shimmer 1.5s infinite;
        border-radius: 4px;
        height: 16px;
        width: 120px;
      }

      &.hidden {
        display: none;
      }
    }
  }

  .header-right {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  &.light-theme {
    --header-background: #ffffff;
    --border-color: #e5e7eb;
    --text-primary: #1f2937;
  }

  &.dark-theme {
    --header-background: #1f2937;
    --border-color: #374151;
    --text-primary: #f9fafb;
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.cursor-pointer {
  cursor: pointer;
}
